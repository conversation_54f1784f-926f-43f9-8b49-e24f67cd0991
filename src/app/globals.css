@import "tailwindcss";

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

@theme {
  --font-sans: 'Arial, sans-serif'; /* Fallback font */
  --font-orbitron: 'Orbitron, sans-serif'; /* Ensure this font is imported */
  --spacing: 0.25rem;
  --radius: 0.625rem;
}

:root {
  /* Light Mode Colors with Pan-African Theme */
  --background: var(--color-pan-white); /* Clean white background */
  --foreground: var(--color-pan-black); /* Black text */
  --card: oklch(0.99 0.005 220); /* Pure white for cards */
  --card-foreground: var(--color-pan-black);
  --popover: oklch(0.99 0.005 220);
  --popover-foreground: var(--color-pan-black);
  --primary: var(--color-pan-green); /* Green for primary actions */
  --primary-foreground: var(--color-pan-white);
  --secondary: var(--color-pan-amber); /* Amber for secondary actions */
  --secondary-foreground: var(--color-pan-black);
  --muted: oklch(0.90 0.02 220); /* Light gray */
  --muted-foreground: oklch(0.40 0.05 270); /* Darker gray for better contrast */
  --accent: var(--color-pan-teal); /* Teal for accents */
  --accent-foreground: var(--color-pan-white);
  --destructive: var(--color-pan-red); /* Red for destructive actions */
  --destructive-foreground: var(--color-pan-white);
  --border: oklch(0.85 0.03 220);
  --input: oklch(0.85 0.03 220);
  --ring: var(--color-pan-teal); /* Teal ring for focus */
  
  /* Chart Colors */
  --chart-1: var(--color-pan-amber); /* Amber */
  --chart-2: var(--color-pan-green); /* Green */
  --chart-3: var(--color-pan-red); /* Red */
  --chart-4: var(--color-pan-teal); /* Teal */
  --chart-5: oklch(0.70 0.05 270); /* Medium gray */
  
  /* Sidebar Colors */
  --sidebar: oklch(0.99 0.005 220);
  --sidebar-foreground: var(--color-pan-black);
  --sidebar-primary: var(--color-pan-green);
  --sidebar-primary-foreground: var(--color-pan-white);
  --sidebar-accent: var(--color-pan-amber);
  --sidebar-accent-foreground: var(--color-pan-black);
  --sidebar-border: oklch(0.85 0.03 220);
  --sidebar-ring: var(--color-pan-teal);
  /* BIPOCA AI Colors - Pan-African Theme */
  --color-gold-300: var(--color-pan-amber); /* Pan-African amber/gold */
  --color-cyan-400: var(--color-pan-green); /* Pan-African green */
  --color-gray-100: var(--color-pan-white); /* White */
  --color-gray-900: var(--color-pan-black); /* Pan-African black */
  --color-white: var(--color-pan-white);
  --color-black: var(--color-pan-black);
  --color-gray-300: oklch(0.90 0.02 220); /* Light gray */
  --color-gray-700: var(--color-pan-teal); /* Pan-African teal */
  
  /* Pan-African Colors */
  --color-pan-green: oklch(0.52 0.18 160); /* Pan-African green #0f7c3d */
  --color-pan-amber: oklch(0.85 0.20 85); /* Pan-African amber/gold #ffc107 */
  --color-pan-red: oklch(0.60 0.25 25); /* Pan-African red #e53935 */
  --color-pan-black: oklch(0.20 0.02 270); /* Pan-African black #1c1c1c */
  --color-pan-teal: oklch(0.55 0.18 190); /* Pan-African teal accent #00897b */
  --color-pan-white: oklch(1.00 0 0); /* White #ffffff */
  /* Shadow Colors for Light Mode */
  --shadow-glow: var(--color-pan-amber); /* Pan-African amber/gold */
  --shadow-opacity: 0.5;
  /* Glassmorphism Colors for Light Mode */
  --glass-background: var(--color-pan-white); /* White */
  --glass-background-opacity: 0.1;
  --glass-border: var(--color-pan-white); /* White */
  --glass-border-opacity: 0.2;
}

.dark {
  /* Dark Mode Colors with Pan-African Theme */
  --background: var(--color-pan-black); /* Black background */
  --foreground: var(--color-pan-white); /* White text */
  --card: oklch(0.25 0.02 270); /* Slightly lighter black for cards */
  --card-foreground: var(--color-pan-white);
  --popover: oklch(0.25 0.02 270);
  --popover-foreground: var(--color-pan-white);
  --primary: var(--color-pan-amber); /* Amber for primary actions */
  --primary-foreground: var(--color-pan-black);
  --secondary: var(--color-pan-green); /* Green for secondary actions */
  --secondary-foreground: var(--color-pan-white);
  --muted: oklch(0.30 0.02 270); /* Dark gray */
  --muted-foreground: oklch(0.80 0.02 270); /* Light gray for better contrast */
  --accent: var(--color-pan-red); /* Red for accents */
  --accent-foreground: var(--color-pan-white);
  --destructive: var(--color-pan-red); /* Red for destructive */
  --destructive-foreground: var(--color-pan-white);
  --border: oklch(0.30 0.02 270);
  --input: oklch(0.30 0.02 270);
  --ring: var(--color-pan-amber); /* Amber ring for focus */
  /* Chart Colors */
  --chart-1: var(--color-pan-amber); /* Amber */
  --chart-2: var(--color-pan-green); /* Green */
  --chart-3: var(--color-pan-red); /* Red */
  --chart-4: var(--color-pan-teal); /* Teal */
  --chart-5: oklch(0.50 0.05 270); /* Medium gray */
  
  /* Sidebar Colors */
  --sidebar: oklch(0.25 0.02 270); /* Slightly lighter black */
  --sidebar-foreground: var(--color-pan-white);
  --sidebar-primary: var(--color-pan-amber);
  --sidebar-primary-foreground: var(--color-pan-black);
  --sidebar-accent: var(--color-pan-red);
  --sidebar-accent-foreground: var(--color-pan-white);
  --sidebar-border: oklch(0.30 0.05 270);
  --sidebar-ring: var(--color-pan-amber);
  /* Dark Mode Adjustments for BIPOCA AI Colors - Pan-African Theme */
  --color-gold-300: var(--color-pan-amber); /* Pan-African amber/gold */
  --color-cyan-400: var(--color-pan-green); /* Pan-African green */
  --color-gray-100: var(--color-pan-white); /* White */
  --color-gray-900: var(--color-pan-black); /* Pan-African black */
  --color-white: var(--color-pan-white);
  --color-black: var(--color-pan-black);
  --color-gray-300: oklch(0.70 0.03 220); /* Light gray */
  --color-gray-700: var(--color-pan-teal); /* Pan-African teal */
  /* Shadow Colors for Dark Mode */
  --shadow-glow: var(--color-pan-green); /* Pan-African green */
  --shadow-opacity: 0.5;
  /* Glassmorphism Colors for Dark Mode */
  --glass-background: var(--color-pan-black); /* Black */
  --glass-background-opacity: 0.2;
  --glass-border: var(--color-pan-amber); /* Pan-African amber/gold */
  --glass-border-opacity: 0.3;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
  /* BIPOCA AI Colors */
  --color-gold-300: var(--color-gold-300);
  --color-cyan-400: var(--color-cyan-400);
  --color-gray-100: var(--color-gray-100);
  --color-gray-900: var(--color-gray-900);
  --color-white: var(--color-white);
  --color-black: var(--color-black);
  --color-gray-300: var(--color-gray-300);
  --color-gray-700: var(--color-gray-700);
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Additional Utilities */
@layer utilities {
  .bg-gradient-radial {
    background-image: radial-gradient(var(--tw-gradient-stops));
  }
  .flex {
    display: flex;
  }
  .grid {
    display: grid;
  }
  .min-h-screen {
    min-height: 100vh;
  }
  .p-4 {
    padding: calc(var(--spacing) * 4);
  }
  .p-8 {
    padding: calc(var(--spacing) * 8);
  }
  .p-12 {
    padding: calc(var(--spacing) * 12);
  }
  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }
  .gap-8 {
    gap: calc(var(--spacing) * 8);
  }
  .text-center {
    text-align: center;
  }
  .text-5xl {
    font-size: 3rem;
  }
  .text-4xl {
    font-size: 2.25rem;
  }
  .text-3xl {
    font-size: 1.875rem;
  }
  .text-2xl {
    font-size: 1.5rem;
  }
  .text-xl {
    font-size: 1.25rem;
  }
  .text-lg {
    font-size: 1.125rem;
  }
  .text-base {
    font-size: 1rem;
  }
  .text-sm {
    font-size: 0.875rem;
  }
  .font-bold {
    font-weight: 700;
  }
  .font-semibold {
    font-weight: 600;
  }
  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }
  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }
  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }
  .justify-center {
    justify-content: center;
  }
  .items-center {
    align-items: center;
  }
  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .sm\:grid-cols-2 {
    @media (min-width: 640px) {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  .md\:grid-cols-3 {
    @media (min-width: 768px) {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }
  }
  .lg\:grid-cols-4 {
    @media (min-width: 1024px) {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  .rounded {
    border-radius: var(--radius);
  }
  .rounded-lg {
    border-radius: calc(var(--radius) * 2);
  }
  /* Shadow Glow Utility */
  .shadow-glow {
    box-shadow: 0 0 15px color-mix(in oklch, var(--shadow-glow), transparent calc((1 - var(--shadow-opacity)) * 100%));
  }
  /* Glassmorphism Utility */
  .glassmorphism {
    background: color-mix(in oklch, var(--glass-background), transparent calc((1 - var(--glass-background-opacity)) * 100%));
    backdrop-filter: blur(5px); /* Reduced blur for better text clarity */
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid color-mix(in oklch, var(--glass-border), transparent calc((1 - var(--glass-border-opacity)) * 100%));
  }
  /* Color Utilities */
  .bg-gold-300 {
    background-color: var(--color-gold-300);
  }
  .bg-cyan-400 {
    background-color: var(--color-cyan-400);
  }
  .text-gold-300 {
    color: var(--color-gold-300);
  }
  .text-cyan-400 {
    color: var(--color-cyan-400);
  }
  .border-gold-300 {
    border-color: var(--color-gold-300);
  }
  .border-cyan-400 {
    border-color: var(--color-cyan-400);
  }
  /* Dark Mode Variants */
  .dark .bg-gold-300 {
    background-color: oklch(0.90 0.30 50);
  }
  .dark .bg-cyan-400 {
    background-color: oklch(0.90 0.30 200);
  }
  /* Gradient Utilities */
  .bg-gradient-gold-cyan {
    background-image: linear-gradient(to right, var(--color-pan-green), var(--color-pan-amber));
  }
  .bg-gradient-gray-dark {
    background-image: linear-gradient(to right, var(--color-gray-100), var(--color-gray-900));
  }
  .dark .bg-gradient-gold-cyan {
    background-image: linear-gradient(135deg, #1c1c1c 0%, #0f2c1d 50%, #1c1c1c 100%) !important;
    border-top: 4px solid var(--color-pan-red);
    border-bottom: 4px solid var(--color-pan-amber);
    position: relative;
    overflow: hidden;
  }
  
  .dark .bg-gradient-gold-cyan::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(to right, transparent, var(--color-pan-teal), transparent);
    opacity: 0.6;
  }
  .dark .bg-gradient-gray-dark {
    background-image: linear-gradient(to right, oklch(0.80 0.02 220), oklch(0.03 0.03 270));
  }
  /* Updated Utility for Text on Gradients */
  .text-gradient-contrast {
    color: #0f2c1d; /* Deep green for light mode */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2); /* Subtle shadow for separation */
  }
  /* Fix for purple text in dark mode */
  .dark .text-gradient-contrast,
  .dark h1,
  .dark h2,
  .dark h3,
  .dark h4,
  .dark h5,
  .dark h6,
  .dark p,
  .dark span,
  .dark .text-foreground,
  .dark [class*="text-"] {
    color: #ffffff !important; /* Pure white for maximum visibility */
    text-shadow: none !important; /* No shadow to prevent blurriness */
    background-image: none !important;
    -webkit-background-clip: initial !important;
    background-clip: initial !important;
  }
  /* Pan-African color scheme */
  :root {
    --color-pan-green: #0f7c3d; /* Green */
    --color-pan-amber: #ffc107; /* Amber/Gold */
    --color-pan-red: #e53935; /* Red */
    --color-pan-black: #1c1c1c; /* Black */
    --color-pan-teal: #00897b; /* Teal accent */
  }
  
  /* Fallback for bg-clip-text issues */
  .bg-clip-text-fallback {
    color: var(--color-pan-amber); /* Amber fallback */
  }
  .dark .bg-clip-text-fallback {
    color: #ffffff; /* Pure white fallback to match text-gradient-contrast */
  }
  /* Accessibility Utilities */
  .text-size-large {
    font-size: 1.5rem;
  }
  .text-size-xlarge {
    font-size: 2rem;
  }
}