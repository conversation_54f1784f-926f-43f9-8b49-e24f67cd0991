import type { <PERSON>ada<PERSON> } from "next";
import { Orbitron } from "next/font/google";
import { Inter } from "next/font/google";
import "./globals.css";
import "./pan-african-theme.css";
import { ThemeProvider } from "@/components/theme-provider";
import Navbar from "@/components/global/navbar";
import Footer from "@/components/global/footer";
import { AuthProvider } from "@/context/AuthContext";
import { Toaster } from "sonner";

const inter = Inter({
  variable: "--font-sans",
  subsets: ["latin"],
});

const orbitron = Orbitron({
  variable: "--font-orbitron",
  subsets: ["latin"],
  weight: ["400", "700"],
});

export const metadata: Metadata = {
  title: "BIPOCA AI",
  description: "Empowering Black, Indigenous, People of Color, and Allies through cutting-edge education and technology.",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body
        className={`${inter.variable} ${orbitron.variable} antialiased min-h-screen flex flex-col w-full bg-background`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem={true}
          disableTransitionOnChange={false}
          storageKey="bipoca-theme"
          themes={["light", "dark", "system", "high-contrast", "visionease"]}
        >
          <AuthProvider>
            <Navbar />
            <main className="flex-1">{children}</main>
            <Footer />
            <Toaster richColors />
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}