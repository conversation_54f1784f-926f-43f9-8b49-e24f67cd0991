rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return request.auth.uid == userId;
    }
    
    function hasRole(role) {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == role;
    }
    
    function isValidUser() {
      return request.resource.data.keys().hasAll(['name', 'email', 'role'])
        && request.resource.data.name is string
        && request.resource.data.email is string
        && request.resource.data.role in ['student', 'teacher', 'parent', 'admin', 'counselor', 'social_worker'];
    }

    // Users collection
    match /users/{userId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && isOwner(userId) && isValidUser();
      allow update: if isAuthenticated() && (isOwner(userId) || hasRole('admin'));
      allow delete: if isAuthenticated() && hasRole('admin');
      
      // User's private data
      match /private/{document=**} {
        allow read, write: if isAuthenticated() && isOwner(userId);
      }
    }

    // Courses collection
    match /courses/{courseId} {
      allow read: if true;
      allow create, update, delete: if isAuthenticated() && (hasRole('admin') || hasRole('teacher'));
      
      // Course content
      match /content/{document=**} {
        allow read: if true;
        allow write: if isAuthenticated() && (hasRole('admin') || hasRole('teacher'));
      }
      
      // Course enrollments
      match /enrollments/{userId} {
        allow read: if isAuthenticated() && (isOwner(userId) || hasRole('admin') || hasRole('teacher'));
        allow write: if isAuthenticated() && (isOwner(userId) || hasRole('admin'));
      }
    }

    // IEP Meetings
    match /iep-meetings/{meetingId} {
      allow read: if isAuthenticated() && (
        hasRole('admin') || 
        hasRole('teacher') || 
        hasRole('counselor') || 
        hasRole('social_worker') ||
        resource.data.participantIds.hasAny([request.auth.uid])
      );
      allow create: if isAuthenticated() && (
        hasRole('admin') || 
        hasRole('teacher') || 
        hasRole('counselor') || 
        hasRole('social_worker')
      );
      allow update: if isAuthenticated() && (
        hasRole('admin') || 
        resource.data.createdBy == request.auth.uid
      );
      allow delete: if isAuthenticated() && hasRole('admin');
    }

    // Live Classes
    match /live-classes/{classId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && hasRole('teacher');
      allow update: if isAuthenticated() && (
        hasRole('teacher') && resource.data.teacherId == request.auth.uid ||
        hasRole('admin')
      );
      allow delete: if isAuthenticated() && hasRole('admin');

      match /attendance/{userId} {
        allow read: if isAuthenticated() && (isOwner(userId) || hasRole('teacher') || hasRole('admin'));
        allow write: if isAuthenticated() && (isOwner(userId) || hasRole('teacher') || hasRole('admin'));
      }
    }

    // Assignments
    match /assignments/{assignmentId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && hasRole('teacher');
      allow update: if isAuthenticated() && (
        hasRole('teacher') && resource.data.teacherId == request.auth.uid ||
        hasRole('admin')
      );
      allow delete: if isAuthenticated() && hasRole('admin');

      match /submissions/{userId} {
        allow read: if isAuthenticated() && (isOwner(userId) || hasRole('teacher') || hasRole('admin'));
        allow create: if isAuthenticated() && isOwner(userId);
        allow update: if isAuthenticated() && (
          (isOwner(userId) && !resource.data.submitted) ||
          hasRole('teacher') ||
          hasRole('admin')
        );
      }
    }

    // Achievements
    match /achievements/{achievementId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && hasRole('admin');

      match /earned/{userId} {
        allow read: if isAuthenticated() && (isOwner(userId) || hasRole('teacher') || hasRole('admin'));
        allow write: if false;  // Only modified by Cloud Functions
      }
    }

    // Chat messages
    match /chat-messages/{messageId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && 
        request.resource.data.userId == request.auth.uid &&
        request.resource.data.keys().hasAll(['message', 'timestamp', 'userId']);
      allow update: if false;  // Messages cannot be edited
      allow delete: if isAuthenticated() && (
        isOwner(resource.data.userId) || 
        hasRole('admin') || 
        hasRole('moderator')
      );
    }

    // Rate limiting
    match /ratelimits/{document=**} {
      allow read, write: if false;  // Only accessible through Cloud Functions
    }
  }
}
